<template>
  <div class="order-items">
    <manage-base-table
      :dataSource="source"
      :columns="materialColumns"
      bordered
      rowKey="uid"
      :refreshButton="false"
      :custom-columns="false"
      ref="tableRef"
    >
      <template #rightButtonArea>
        <a-button type="dashed" v-if="hasAdd == true" @click="addMaterialBatch">
          <template #icon><PlusOutlined /></template>
          添加物料
        </a-button>
      </template>
      <!-- 自定义列渲染 -->
      <template #bodyCell="{ column, record, index }">
        <!-- 数量列 -->
        <template v-if="column.key === 'quantity'">
          <a-form-item :name="['items', index, 'quantity']" class="no-margin">
            <a-input-number
              v-model:value="record.quantity"
              placeholder="数量"
              :disabled="isView"
              :min="0.0001"
              :max="
                props.hasBatchNo && record.batch_no
                  ? getStockQuantity(record.id, record.batch_no)
                  : undefined
              "
              style="width: 100%"
              @change="(value) => handleQuantityChange(value, record, index)"
            />
          </a-form-item>
        </template>

        <!-- 单价列 -->
        <template v-if="column.key === 'price'">
          <a-form-item :name="['items', index, 'price']" class="no-margin">
            <a-input-number
              v-model:value="record.price"
              placeholder="单价"
              :disabled="isView"
              :min="0"
              style="width: 100%"
              @change="() => calculateTotalAmount()"
            />
          </a-form-item>
        </template>

        <!-- 备注列 -->
        <template v-if="column.key === 'note'">
          <a-form-item :name="['items', index, 'note']" class="no-margin">
            <a-input
              v-model:value="record.note"
              placeholder="备注"
              :disabled="isView"
            />
          </a-form-item>
        </template>

        <!-- 操作列 -->
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button
              v-if="hasSelected == true"
              type="link"
              @click="() => emits('selected', record)"
            >
              {{ selectedText }}
            </a-button>
            <a-button
              v-if="hasDelete"
              type="text"
              danger
              @click="removeMaterial(index)"
            >
              删除
            </a-button>
          </a-space>
        </template>
        <template v-if="column.key === 'batch_no'">
          <!-- <a-form-item :name="['items', index, 'batch_no']" class="no-margin">
            <a-select
              v-model:value="record.batch_no"
              placeholder="选择批号"
              @change="(value) => handleBatchNoChange(record, value)"
              @dropdownVisibleChange="
                (open) => handleDropdownVisibleChange(open, record)
              "
              :loading="loadingBatchNo === record.id"
            >
              <a-select-option
                v-for="stock in getMaterielStockBatches(record.id)"
                :key="stock.batch_no"
                :value="stock.batch_no"
              >
                {{ stock.batch_no }} (库存: {{ stock.quantity }})
              </a-select-option>
            </a-select>
          </a-form-item> -->
          <a-dropdown
            trigger="click"
            @openChange="(open) => handleDropdownVisibleChange(open, record)"
          >
            <template #overlay>
              <a-menu>
                <a-menu-item
                  v-if="getMaterielStockBatches(record.id).length === 0"
                  disabled
                >
                  无可用库存
                </a-menu-item>
                <a-menu-item
                  v-else
                  v-for="stock in getMaterielStockBatches(record.id)"
                  :key="stock.batch_no"
                  :value="stock.batch_no"
                  @click="handleBatchNoChange(record, stock)"
                >
                  {{ stock.batch_no }} (库存: {{ stock.quantity }})
                </a-menu-item>
              </a-menu>
            </template>
            <a-button :loading="loadingBatchNo === record.id">
              {{ record.batch_no || "选择批号" }}
              <DownOutlined />
            </a-button>
          </a-dropdown>
        </template>
      </template>
    </manage-base-table>
    <!-- 添加物料按钮 -->
    <!-- <div class="add-item" v-if="!isView">
      <a-button type="dashed" block @click="addMaterialBatch">
        <PlusOutlined /> 添加物料
      </a-button>
    </div> -->

    <!-- 总计 -->
    <template v-if="props.hasPrice">
      <div class="total-amount">
        <span>订单总金额: </span>
        <span class="amount">{{ formatCurrency(totalAmount) }}</span>
      </div>
    </template>

    <!-- 批量添加物料模态框 -->
    <manage-materiel-modelselector
      @selected="onBatchMaterielSelect"
      :multiple="true"
      v-model:visible="materielModalVisible"
    />
  </div>
</template>
<script setup lang="ts">
  import { v4 as uuidv4 } from "uuid";
  import Decimal from "decimal.js";
  import type { SelectValue } from "ant-design-vue/es/select";
  import { useApiTrpc } from "#imports";

  // 添加类型定义
  interface StockItem {
    id: number;
    materiel_id: number;
    warehouse_id: number;
    batch_no: string;
    quantity: number | Decimal;
  }

  interface MaterielListItem {
    id: number;
    uid: string;
    name: string;
    code: string;
    specification?: string;
    unit?: string;
    model?: string;
    quantity: number;
    price: number;
    note: string;
    batch_no?: string;
    warehouse_id?: number;
  }

  const props = defineProps({
    hasAdd: {
      type: Boolean,
      default: true,
    },
    hasPrice: {
      type: Boolean,
      default: false,
    },
    hasNote: {
      type: Boolean,
      default: false,
    },
    isView: {
      type: Boolean,
      default: false,
    },
    hasBatchNo: {
      type: Boolean,
      default: false,
    },
    hasDelete: {
      type: Boolean,
      default: false,
    },
    hasSelected: {
      type: Boolean,
      default: false,
    },
    selectedText: {
      type: String,
      default: "选择",
    },
  });
  const emits = defineEmits<{
    (e: "selected", items: MaterielListItem[]): void;
  }>();
  const source = defineModel<MaterielListItem[]>("source", {
    default: () => [] as MaterielListItem[],
  });

  // 添加库存批号相关的状态
  const stockMap = ref(new Map<number, StockItem[]>());
  const loadingBatchNo = ref<number | null>(null);

  // 处理下拉框展开/收起
  const handleDropdownVisibleChange = async (
    open: boolean,
    record: MaterielListItem
  ) => {
    // 保存当前数量，防止在下拉框操作过程中被意外重置
    const currentQuantity = record.quantity;
    console.log("下拉框操作前的数量:", currentQuantity);

    if (open) {
      // stockMap.value.clear();
      loadingBatchNo.value = record.id;
      try {
        // 每次打开下拉框时都重新查询库存
        await queryMaterielStock(record.id);
      } finally {
        loadingBatchNo.value = null;
      }
    }

    // 确保数量不会在下拉框操作过程中被意外重置
    if (currentQuantity > 0 && record.quantity === 0) {
      console.log("下拉框操作导致数量被重置，恢复为:", currentQuantity);
      record.quantity = currentQuantity;
    }

    console.log("下拉框操作后的数量:", record.quantity);
  };

  // 查询物料库存批号
  const queryMaterielStock = async (materielId: number) => {
    try {
      // 保存当前物料在表格中的数量值，以防在查询过程中被意外修改
      const currentItems = source.value.filter(
        (item) => item.id === materielId
      );
      const quantityMap = new Map<string, number>();

      // 记录每个物料当前的数量
      currentItems.forEach((item) => {
        const key = `${item.id}-${item.uid}`;
        quantityMap.set(key, item.quantity);
      });

      // 查询指定物料的批号库存
      const client = useApiTrpc();
      const result = await client.admin.stock.queryMaterielBatches.query({
        materielId: materielId,
        warehouseId: undefined, // 可以根据需要传入仓库ID进行过滤
      });

      if (result.code === 1 && result.data) {
        // 转换为 StockItem 格式并更新 stockMap
        const stockItems: StockItem[] = result.data.map((item: any) => ({
          id: item.id,
          materiel_id: item.materiel_id,
          warehouse_id: item.warehouse_id,
          batch_no: item.batch_no,
          quantity: item.quantity,
        }));

        // 更新 stockMap
        stockMap.value.set(materielId, stockItems);
      }

      // 检查并恢复可能被修改的数量值
      currentItems.forEach((item) => {
        const key = `${item.id}-${item.uid}`;
        const savedQuantity = quantityMap.get(key);

        if (savedQuantity && savedQuantity > 0 && item.quantity === 0) {
          console.log(`物料${item.id}的数量被重置，恢复为:`, savedQuantity);
          item.quantity = savedQuantity;
        }
      });
    } catch (error) {
      console.error("查询库存批号失败:", error);
      message.error("获取库存批号失败");
    }
  };

  // 获取物料的库存批号列表
  const getMaterielStockBatches = (materielId: number) => {
    console.log("获取物料库存批号列表:", materielId);
    const stocks = stockMap.value.get(materielId) || [];
    console.log("当前物料的库存记录:", stocks);
    // 只返回有库存的批号
    const result = stocks.filter((stock) => Number(stock.quantity) > 0);
    console.log("过滤后的批号列表:", result);
    return result;
  };

  // 获取批号的可用库存数量
  const getStockQuantity = (materielId: number, batchNo: string) => {
    const stocks = getMaterielStockBatches(materielId);
    const stock = stocks.find((item) => item.batch_no === batchNo);
    return stock ? Number(stock.quantity) : 0;
  };

  const query = computed(() => {
    return async (params?: any) => {
      return { data: { result: source.value, total: source.value.length } };
    };
  });
  const showAction = computed(() => {
    return props.hasSelected || props.hasDelete;
  });
  const materialMap = ref(new Map<number, MaterielListItem>());
  const materialColumns: ExtendedTableColumnProps<MaterielListItem>[] = [
    {
      title: "物料编码",
      dataIndex: "code",
      key: "code",
      width: "10%",
    },
    {
      title: "物料名称",
      dataIndex: "name",
      key: "name",
      width: "20%",
    },
    {
      title: "规格",
      dataIndex: "spec",
      key: "spec",
      width: "10%",
      customRender: (opt) => {
        return opt.record.specification || "-";
      },
    },
    {
      title: "单位",
      dataIndex: "unit",
      key: "unit",
      width: "8%",
      customRender: (opt) => {
        return opt.record.unit || "-";
      },
    },
    {
      title: "批号",
      dataIndex: "batch_no",
      key: "batch_no",
      width: "12%",
      defaultVisible: props.hasBatchNo,
    },
    { title: "数量", dataIndex: "quantity", key: "quantity", width: "12%" },
    {
      title: "单价",
      dataIndex: "price",
      key: "price",
      width: "12%",
      defaultVisible: props.hasPrice,
    },
    {
      title: "总价",
      dataIndex: "totalPrice",
      key: "totalPrice",
      width: "12%",
      defaultVisible: props.hasPrice,
      customRender: (opt) => {
        const quantity = Number(opt.record.quantity) || 0;
        const price = Number(opt.record.price) || 0;
        return formatCurrency(quantity * price);
      },
    },
    {
      title: "备注",
      dataIndex: "note",
      key: "note",
      width: "12%",
      defaultVisible: props.hasNote,
    },
    {
      title: "操作",
      key: "action",
      width: "10%",
      defaultVisible: showAction.value,
    },
  ];
  const totalAmount = ref(0);
  // 计算订单总金额
  const calculateTotalAmount = () => {
    totalAmount.value = source.value.reduce((sum, item) => {
      const quantity = Number(item.quantity) || 0;
      const price = Number(item.price) || 0;
      return sum + quantity * price;
    }, 0);
  };
  // 格式化金额
  const formatCurrency = (amount: number) => {
    if (isNaN(amount)) return "¥ 0.00";
    return `¥ ${amount.toFixed(2)}`;
  };
  const materielModalVisible = ref(false);
  // 添加批量添加物料的方法
  const addMaterialBatch = () => {
    materielModalVisible.value = true;
  };
  const tableRef = ref();
  // 批量物料选择响应
  const onBatchMaterielSelect = async (selectedItems: MaterielListItem[]) => {
    console.log("选择的物料:", selectedItems);
    if (!selectedItems || selectedItems.length === 0) return;

    try {
      // 添加物料到表格
      for (const item of selectedItems) {
        console.log("添加物料到表格:", item);
        // 添加到表格
        source.value.push({
          uid: uuidv4(),
          id: item.id,
          name: item.name,
          quantity: 1,
          price: 0,
          note: "",
          model: item.model,
          code: item.code,
          specification: item.specification,
          unit: item.unit,
          batch_no: "",
        });

        // 将物料信息添加到映射表
        if (!materialMap.value.has(item.id)) {
          materialMap.value.set(item.id, {
            id: item.id,
            name: item.name,
            specification: item.specification,
            unit: item.unit,
            quantity: 0,
            price: 0,
            model: item.model,
            note: "",
            uid: "",
            code: item.code,
          });
        }
      }
      calculateTotalAmount();
      materielModalVisible.value = false;
    } catch (error) {
      console.error("处理物料选择时出错:", error);
    }
  };
  // 批号选择变更处理
  const handleBatchNoChange = (record: MaterielListItem, stock: any) => {
    const batchNo = stock.batch_no;
    const warehouse_id = stock.warehouse_id;
    console.log("批号选择变更:", record, batchNo, warehouse_id);
    if (typeof batchNo === "string") {
      // 保存当前数量，防止被意外重置
      const currentQuantity = record.quantity;
      console.log("变更前的数量:", currentQuantity);

      // 设置批号
      record.batch_no = batchNo;
      // 设置仓库ID
      record.warehouse_id = warehouse_id;

      // 获取最大库存数量
      const maxQuantity = getStockQuantity(record.id, batchNo);
      console.log("获取到的最大库存数量:", maxQuantity);

      // 确保数量不会被意外重置为0
      if (currentQuantity > 0 && record.quantity === 0) {
        console.log("数量被意外重置，恢复为原值:", currentQuantity);
        record.quantity = currentQuantity;
      }

      // 检查库存是否足够
      if (maxQuantity < record.quantity) {
        message.error(`库存不足,当前库存为${maxQuantity},请检查`);
        // 不自动调整数量，只提示用户
      }

      // 再次确认数量没有被重置
      console.log("变更后的最终数量:", record.quantity);
    }
  };
  // 处理数量变更
  const handleQuantityChange = (
    value: number | string,
    record: MaterielListItem,
    index: number
  ) => {
    console.log("数量变更:", value, record);

    // 确保value是数字类型
    const numValue = typeof value === "string" ? parseFloat(value) : value;

    // 如果是有效数字，则更新数量
    if (!isNaN(numValue)) {
      // 直接设置数量，确保不会被意外重置
      record.quantity = numValue;

      // 如果有批号，检查库存是否足够
      if (props.hasBatchNo && record.batch_no) {
        const maxQuantity = getStockQuantity(record.id, record.batch_no);
        if (maxQuantity < numValue) {
          message.error(`库存不足,当前库存为${maxQuantity},请检查`);
        }
      }
    } else if (value === null || value === undefined || value === "") {
      // 如果输入为空，设置为最小值
      record.quantity = 0.0001;
    }

    // 更新总金额
    calculateTotalAmount();
  };

  // 移除物料行
  const removeMaterial = (index: number) => {
    source.value.splice(index, 1);
    calculateTotalAmount();
  };
</script>
<style scoped>
  .total-amount {
    text-align: right;
    margin-top: 16px;
    font-size: 16px;
    font-weight: bold;
  }

  .amount {
    color: #1890ff;
    margin-left: 8px;
  }
  .add-item {
    margin-top: 16px;
    margin-bottom: 16px;
  }
  :deep(.no-margin) {
    margin-bottom: 0;
  }
</style>
